# @package hydra/sweeper
# 简化的Optuna sweeper配置用于测试
# 使用更小的参数范围以便快速测试和减少内存使用

_target_: hydra_plugins.hydra_optuna_sweeper.optuna_sweeper.OptunaSweeper

# storage for Optuna study
storage: ${optuna_storage} # 从主配置引用
study_name: ${optuna_study_name} # 从主配置引用

# 添加显式配置，确保正确处理 SQLite 存储
storage_options:
  url_connect_args:
    timeout: 60  # 增加连接超时时间
    check_same_thread: false  # 允许多线程访问

# number of parallel workers
n_jobs: 1

# 'minimize' or 'maximize'
direction: minimize

# number of trials in the study
n_trials: ${optuna_trials} # 从主配置引用

# Optuna Sampler configuration
sampler:
  _target_: optuna.samplers.TPESampler
  seed: ${seed} # 使用全局种子

# 简化的参数优化范围（适合小数据集测试）
params:
  trainer.learning_rate: tag(log, interval(5e-5, 2e-4))     # 缩小学习率范围
  trainer.batch_size: choice(1, 2)                          # 非常小的批次大小
  model.d_embed: choice(64, 128)                            # 简化嵌入维度选择
  model.llm_layers_to_use: range(2, 4)                      # 只使用2-3层以节省内存
  model.U_unfrozen_mha: choice(1, 2)                        # 简化MHA选择
  trainer.optimizer_type: choice(AdamW)                      # 只使用AdamW